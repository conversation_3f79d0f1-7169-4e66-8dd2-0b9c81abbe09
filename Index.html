<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="theme-color" content="#ff0055">
  <title>Player 456 | Squid Game NFC Site</title>
  <meta name="description" content="Ultra-responsive Squid Game themed NFC-activated portfolio site with immersive audio, animations, and accessibility features.">
  <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
  <link rel="manifest" href="manifest.json">
  <link rel="stylesheet" href="styles.css">
  <script defer src="https://cdn.jsdelivr.net/npm/howler@2.2.4/dist/howler.min.js"></script>
  <script defer src="https://cdn.jsdelivr.net/npm/particles.js"></script>
  <!-- Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Poppins:wght@300;600&display=swap" rel="stylesheet"> 
</head>
<body>
  <!-- Particle Background -->
  <div id="particles-js"></div>
  
  <!-- Accessibility Panel -->
  <div class="accessibility-panel" aria-label="Accessibility options">
    <button id="contrast-toggle" aria-label="Toggle color contrast"><span>🌓</span></button>
    <button id="motion-toggle" aria-label="Toggle reduced motion"><span>🎬</span></button>
    <button id="dark-toggle" aria-label="Toggle dark mode"><span>🎭</span></button>
  </div>

  <main>
    <!-- Section 1: Hero Zone -->
    <section id="hero" class="zone">
      <div class="logo-intro" aria-label="Animated Squid Game Logo">
        <svg viewBox="0 0 300 80" class="squid-logo" aria-hidden="true">
          <defs>
            <linearGradient id="logoGrad" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#ff0055;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#00ffaa;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="squidGameGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
              <stop offset="30%" style="stop-color:#ffffff;stop-opacity:1" />
              <stop offset="60%" style="stop-color:#ff1744;stop-opacity:1" />
              <stop offset="80%" style="stop-color:#e91e63;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#ff1744;stop-opacity:1" />
            </linearGradient>
          </defs>
          <g class="logo-particles">
            <circle cx="50" cy="40" r="3" fill="#ff0055" class="particle p1"/>
            <circle cx="80" cy="25" r="2" fill="#00ffaa" class="particle p2"/>
            <circle cx="120" cy="55" r="2.5" fill="#ff0055" class="particle p3"/>
            <circle cx="180" cy="30" r="2" fill="#00ffaa" class="particle p4"/>
            <circle cx="220" cy="50" r="3" fill="#ff0055" class="particle p5"/>
            <circle cx="250" cy="35" r="2" fill="#00ffaa" class="particle p6"/>
          </g>
          <text x="150" y="50" text-anchor="middle" fill="url(#squidGameGradient)" class="logo-text">
            <tspan>SQUID</tspan>
            <tspan dx="10" fill="#ff1744">GAME</tspan>
          </text>
        </svg>
        <canvas id="particle-canvas" aria-hidden="true"></canvas>
      </div>
      <div class="player-badge" tabindex="0" aria-label="Player Badge">
        <span class="player-number" id="player-number"></span>
        <span class="player-name">Fahim</span>
      </div>
      <nav class="social-grid" aria-label="Social Links">
        <a href="https://instagram.com/yourprofile" class="social-icon instagram" aria-label="Instagram" target="_blank" rel="noopener">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
          </svg>
        </a>
        <a href="https://github.com/yourprofile" class="social-icon github" aria-label="GitHub" target="_blank" rel="noopener">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
          </svg>
        </a>
        <a href="https://linkedin.com/in/yourprofile" class="social-icon linkedin" aria-label="LinkedIn" target="_blank" rel="noopener">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
          </svg>
        </a>
        <a href="https://twitter.com/yourprofile" class="social-icon twitter" aria-label="Twitter" target="_blank" rel="noopener">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
          </svg>
        </a>
      </nav>

      <!-- Player Card Section -->
      <div class="player-card">
        <!-- Geometric Shapes Background -->
        <div class="geometric-shapes">
          <div class="shape triangle top-left"></div>
          <div class="shape circle top-right"></div>
          <div class="shape square center-left"></div>
          <div class="shape triangle center-right"></div>
          <div class="shape circle bottom-left"></div>
          <div class="shape square bottom-right"></div>
        </div>

        <!-- Player Image Upload Section -->
        <div class="player-image-section">
          <div class="image-upload-area" onclick="document.getElementById('imageUpload').click()">
            <div class="upload-placeholder" id="uploadPlaceholder">
              <div>📷</div>
              <div>Click to add your photo</div>
            </div>
            <img id="playerImage" class="player-image" style="display: none;" alt="Player Photo">
          </div>
          <input type="file" id="imageUpload" class="file-input" accept="image/*">
        </div>

        <!-- Player Number -->
        <div class="player-number">456</div>
      </div>
    </section>

    <!-- Section 2: Link Hub -->
    <section id="link-hub" class="zone">
      <div class="progress-bar" aria-label="Loading progress">
        <div class="progress-bar-bg">
          <div class="progress-bar-fill"></div>
        </div>
        <span class="progress-label">Red Light</span>
      </div>
      <div class="carousel-3d" aria-label="Project Carousel">
        <div class="carousel-card" data-project="1">
          <div class="card-front">
            <h3>NFC Portfolio</h3>
            <p>Interactive Squid Game themed website</p>
          </div>
          <div class="card-back">
            <h4>Tech Stack</h4>
            <ul>
              <li>HTML5 + CSS3</li>
              <li>Vanilla JavaScript</li>
              <li>Web NFC API</li>
              <li>Particle.js</li>
            </ul>
          </div>
        </div>
        <div class="carousel-card" data-project="2">
          <div class="card-front">
            <h3>Project Alpha</h3>
            <p>Your next amazing project</p>
          </div>
          <div class="card-back">
            <h4>Features</h4>
            <ul>
              <li>Responsive Design</li>
              <li>Modern UI/UX</li>
              <li>Performance Optimized</li>
              <li>Accessibility First</li>
            </ul>
          </div>
        </div>
        <div class="carousel-card" data-project="3">
          <div class="card-front">
            <h3>Project Beta</h3>
            <p>Another innovative solution</p>
          </div>
          <div class="card-back">
            <h4>Highlights</h4>
            <ul>
              <li>Cross-platform</li>
              <li>Real-time Updates</li>
              <li>Secure Architecture</li>
              <li>Scalable Design</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    <!-- Section 3: Project Showcase -->
    <section id="showcase" class="zone">
      <div class="project-grid" aria-label="Project Grid">
        <div class="project-item" data-number="001">
          <div class="project-image">
            <div class="placeholder-img">NFC Portfolio</div>
          </div>
          <div class="project-overlay">
            <h4>Player 001</h4>
            <p>Squid Game NFC Website</p>
            <button class="view-btn">View Project</button>
          </div>
        </div>
        <div class="project-item" data-number="067">
          <div class="project-image">
            <div class="placeholder-img">Project Alpha</div>
          </div>
          <div class="project-overlay">
            <h4>Player 067</h4>
            <p>Modern Web Application</p>
            <button class="view-btn">View Project</button>
          </div>
        </div>
        <div class="project-item" data-number="218">
          <div class="project-image">
            <div class="placeholder-img">Project Beta</div>
          </div>
          <div class="project-overlay">
            <h4>Player 218</h4>
            <p>Mobile-First Design</p>
            <button class="view-btn">View Project</button>
          </div>
        </div>
        <div class="project-item eliminated" data-number="456">
          <div class="project-image">
            <div class="placeholder-img">Legacy Project</div>
          </div>
          <div class="project-overlay">
            <h4>Player 456</h4>
            <p>Eliminated Project</p>
            <button class="view-btn" disabled>Eliminated</button>
          </div>
        </div>
      </div>
    </section>

    <!-- Section 4: Contact Arena -->
    <section id="contact" class="zone">
      <div class="contact-icons">
        <button class="icon-btn phone" aria-label="Phone"></button>
        <button class="icon-btn mail" aria-label="Mail"></button>
      </div>
      <form id="contact-form" autocomplete="off" novalidate>
        <label>
          <span>Name</span>
          <input type="text" name="name" required aria-required="true">
        </label>
        <label>
          <span>Email</span>
          <input type="email" name="email" required aria-required="true">
        </label>
        <label>
          <span>Message</span>
          <textarea name="message" required aria-required="true"></textarea>
        </label>
        <button type="submit" class="submit-btn">Play!</button>
      </form>
    </section>
  </main>

  <!-- Game Over 404 Modal - Initially removed, will be created by JS when needed -->
  <!-- <div id="game-over-modal" aria-modal="true" role="dialog" hidden>
    <div class="guard-mask-anim"></div>
    <h1>Game Over</h1>
    <p>404: The page you are looking for has been eliminated.</p>
    <button id="close-404">Retry</button>
  </div> -->

  <!-- Custom Cursor -->
  <div id="custom-cursor"></div>

  <!-- Emergency script to force hide any modal -->
  <script>
    // Immediately hide any modal that might exist
    function forceHideModal() {
      const modal = document.getElementById('game-over-modal');
      if (modal) {
        modal.remove();
        console.log('Modal forcefully removed');
      }
      // Force show main content
      const main = document.querySelector('main');
      if (main) {
        main.style.display = 'block !important';
        main.style.visibility = 'visible !important';
        main.style.opacity = '1 !important';
        console.log('Main content forced visible');
      }
    }

    // Run immediately
    forceHideModal();

    // Run on DOM ready
    document.addEventListener('DOMContentLoaded', forceHideModal);

    // Run after a delay to catch any late-loading modals
    setTimeout(forceHideModal, 500);
  </script>

  <!-- Particles.js -->
  <script src="https://cdn.jsdelivr.net/npm/particles.js"></script>
  <!-- Howler.js for audio -->
  <script src="https://cdn.jsdelivr.net/npm/howler@2.2.4/dist/howler.min.js"></script>
  <!-- NFC / Audio / Effects / PWA logic -->
  <script src="main.js" defer></script>

  <!-- Note: This file is renamed to lowercase index.html for proper browser compatibility. -->
  <!-- Please use index.html as your entry point. -->
</body>
</html>