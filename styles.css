:root {
  --pink: #ff0055;
  --mint: #00ffaa;
  --white: #fff;
  --black: #090909;
  --header-font: '<PERSON><PERSON>', sans-serif;
  --body-font: 'Poppins', sans-serif;
  --player-badge-bg: linear-gradient(90deg, #ff0055 60%, #00ffaa 100%);
  --guard-red: #d90429;
  --guard-black: #232323;
  --highlight: #ff0055;
  --transition-speed: 0.35s cubic-bezier(0.77,0,0.175,1);
}

/* Global Reset & Body */
html, body {
  scroll-behavior: smooth;
  background: var(--black);
  color: var(--white);
  font-family: var(--body-font);
  min-height: 100vh;
  margin: 0; padding: 0;
  position: relative;
  overflow-x: hidden;
}
body {
  transition: background 0.4s, color 0.4s;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}
img {
  max-width: 100%;
  height: auto;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *, *::before, *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Accessibility Panel */
.accessibility-panel {
  position: fixed; top: 1rem; right: 1rem; z-index: 1100;
  display: flex; gap: 0.5rem;
  background: rgba(9,9,9,0.8); border-radius: 2rem;
  padding: 0.5rem 1rem;
}
.accessibility-panel button {
  background: var(--mint);
  color: var(--black);
  border: none;
  border-radius: 50%;
  width: 2rem; height: 2rem;
  font-size: 1.25rem;
  cursor: pointer;
  transition: background 0.2s;
}
.accessibility-panel button:focus {
  outline: 2px solid var(--pink);
}

/* Particle BG */
#particles-js {
  position: fixed; z-index: 0; inset: 0;
  pointer-events: none;
}

/* Main content */
main {
  position: relative;
  z-index: 1;
  min-height: 100vh;
}

/* Hero Section */
#hero {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 2rem;
  background: linear-gradient(150deg, var(--black) 80%, var(--pink) 100%);
  position: relative;
  z-index: 1;
}
.logo-intro {
  margin-top: 1.5rem;
  width: 260px; height: 80px;
  position: relative;
  animation: glitch 2.5s infinite linear alternate;
}
.squid-logo {
  width: 100%; height: 100%;
}
.squid-logo .logo-text {
  font-family: var(--header-font);
  font-size: 18px;
  font-weight: bold;
  letter-spacing: 2px;
  animation: logo-glow 3s ease-in-out infinite alternate;
}
.squid-logo .particle {
  animation: float-particles 4s ease-in-out infinite;
}
.squid-logo .p1 { animation-delay: 0s; }
.squid-logo .p2 { animation-delay: 0.5s; }
.squid-logo .p3 { animation-delay: 1s; }
.squid-logo .p4 { animation-delay: 1.5s; }
.squid-logo .p5 { animation-delay: 2s; }
.squid-logo .p6 { animation-delay: 2.5s; }

@keyframes logo-glow {
  0%, 100% { filter: drop-shadow(0 0 5px var(--pink)); }
  50% { filter: drop-shadow(0 0 20px var(--mint)) drop-shadow(0 0 30px var(--pink)); }
}
@keyframes float-particles {
  0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
  25% { transform: translateY(-10px) rotate(90deg); opacity: 1; }
  50% { transform: translateY(-5px) rotate(180deg); opacity: 0.8; }
  75% { transform: translateY(-15px) rotate(270deg); opacity: 1; }
}
#particle-canvas {
  position: absolute; inset: 0;
  pointer-events: none;
}

.player-badge {
  font-family: var(--header-font);
  background: var(--player-badge-bg);
  color: var(--white);
  font-size: 2.1rem;
  border-radius: 1.5rem;
  padding: 0.7em 2em 0.7em 1.5em;
  box-shadow: 0 0 16px var(--mint);
  display: flex; align-items: center;
  gap: 0.7em;
  cursor: pointer;
  transition: box-shadow var(--transition-speed);
  position: relative;
}
.player-badge:hover, .player-badge:focus {
  box-shadow: 0 0 48px var(--pink), 0 0 8px var(--mint);
}
.player-number {
  background: var(--black);
  color: var(--mint);
  border-radius: 0.5em;
  font-size: 1.4em;
  padding: 0.1em 0.5em;
  margin-right: 0.5em;
  transition: background 0.3s, color 0.3s;
}
.player-badge:hover .player-number,
.player-badge:focus .player-number {
  background: var(--mint);
  color: var(--black);
}

/* Social Hex Grid */
.social-grid {
  display: flex; gap: 1.5rem;
  flex-wrap: wrap;
  margin-top: 1rem;
  justify-content: center;
}
.social-icon {
  width: 3.5rem; height: 3.5rem;
  background: var(--mint);
  clip-path: polygon(
    25% 5%, 75% 5%,
    100% 50%, 75% 95%,
    25% 95%, 0% 50%
  );
  display: flex; align-items: center; justify-content: center;
  box-shadow: 0 0 20px var(--mint), 0 0 2px var(--pink);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  cursor: pointer;
  will-change: transform, filter, background;
  color: var(--black);
  text-decoration: none;
}
.social-icon svg {
  width: 1.8rem; height: 1.8rem;
  transition: transform 0.3s;
}
.social-icon:hover, .social-icon:focus {
  animation: liquid-melt 1.3s cubic-bezier(0.7,0.7,0.3,1.2) infinite alternate;
  filter: drop-shadow(0 0 24px var(--pink));
  background: var(--pink);
  color: var(--white);
  transform: scale(1.1);
}
.social-icon:hover svg {
  transform: rotate(15deg) scale(1.1);
}
@keyframes liquid-melt {
  0%,100% { filter: blur(0px) }
  30% { filter: blur(2px) }
  70% { filter: blur(6px) }
}

/* Section: Link Hub */
#link-hub {
  background: var(--black);
  padding: 4em 0 2em;
}
.progress-bar {
  width: 90%; max-width: 360px; margin: 0 auto 2.5em;
  text-align: center;
}
.progress-bar-bg {
  width: 100%; height: 1.2em; background: var(--mint);
  border-radius: 0.6em;
  overflow: hidden;
  box-shadow: 0 0 12px var(--mint);
}
.progress-bar-fill {
  width: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--pink), var(--mint));
  animation: redgreen-progress 2.2s linear forwards;
}
@keyframes redgreen-progress {
  to { width: 100%; }
}
.progress-label {
  display: block;
  margin-top: 0.3em;
  color: var(--pink);
  font-family: var(--header-font);
  letter-spacing: 0.12em;
}

/* 3D Carousel */
.carousel-3d {
  display: flex;
  flex-direction: row;
  justify-content: center;
  gap: 1.5rem;
  margin: 2em auto;
  perspective: 1200px;
  min-height: 240px;
  width: 100vw;
  max-width: 425px;
  position: relative;
}
.carousel-card {
  width: 180px; height: 230px;
  background: var(--white);
  color: var(--black);
  border-radius: 1.2em;
  box-shadow: 0 0 22px var(--mint);
  transform-style: preserve-3d;
  transition: transform 0.7s cubic-bezier(0.6,0.2,0.3,1.3), box-shadow 0.3s;
  cursor: pointer;
  position: absolute;
  top: 0; left: 50%;
  transform: translateX(-50%) rotateY(var(--rotation, 0deg));
  z-index: var(--z, 1);
}
.carousel-card:hover, .carousel-card:focus {
  box-shadow: 0 0 44px var(--pink);
  z-index: 5;
}
.carousel-card .card-front,
.carousel-card .card-back {
  position: absolute; inset: 0;
  backface-visibility: hidden;
  padding: 1.5em;
  display: flex; flex-direction: column; justify-content: center; align-items: center;
  border-radius: 1.2em;
  text-align: center;
}
.carousel-card .card-back {
  transform: rotateY(180deg);
  background: var(--mint);
  color: var(--black);
}
.carousel-card:hover {
  transform: translateX(-50%) rotateY(180deg);
}
.carousel-card h3, .carousel-card h4 {
  font-family: var(--header-font);
  margin: 0 0 0.5em 0;
  font-size: 1.4em;
  color: var(--pink);
}
.carousel-card .card-back h4 {
  color: var(--black);
}
.carousel-card ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.carousel-card li {
  padding: 0.3em 0;
  font-size: 0.9em;
  border-bottom: 1px solid rgba(0,0,0,0.1);
}

/* Section: Project Showcase */
#showcase {
  background: var(--mint);
  padding: 4em 0 2em;
}
.project-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
  gap: 1.8em;
  margin: 0 auto;
  max-width: 1000px;
}
.project-item {
  background: var(--white);
  border-radius: 1.2em;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px var(--guard-black);
  min-height: 200px;
}
.project-item::before {
  content: attr(data-number);
  position: absolute;
  top: 0.8rem;
  left: 0.8rem;
  z-index: 3;
  font-family: var(--header-font);
  font-size: 1.2em;
  background: var(--pink);
  color: var(--white);
  border-radius: 0.5em;
  padding: 0.3em 0.8em;
  box-shadow: 0 0 6px var(--guard-black);
  opacity: 0.9;
}
.project-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(255, 0, 85, 0.3);
}
.project-image {
  width: 100%;
  height: 140px;
  background: linear-gradient(135deg, var(--black), var(--guard-black));
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.placeholder-img {
  color: var(--mint);
  font-family: var(--header-font);
  font-size: 1.1em;
  text-align: center;
}
.project-overlay {
  padding: 1rem;
  background: var(--white);
  color: var(--black);
}
.project-overlay h4 {
  font-family: var(--header-font);
  margin: 0 0 0.5rem 0;
  color: var(--pink);
  font-size: 1.3em;
}
.project-overlay p {
  margin: 0 0 1rem 0;
  font-size: 0.9em;
  color: var(--guard-black);
}
.view-btn {
  background: var(--mint);
  color: var(--black);
  border: none;
  padding: 0.5em 1em;
  border-radius: 0.5em;
  font-family: var(--body-font);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}
.view-btn:hover:not(:disabled) {
  background: var(--pink);
  color: var(--white);
  transform: scale(1.05);
}
.view-btn:disabled {
  background: var(--guard-black);
  color: var(--white);
  cursor: not-allowed;
}
.project-item.eliminated {
  filter: grayscale(1) opacity(0.7);
}
.project-item.eliminated:hover::after {
  content: "ELIMINATED!";
  position: absolute;
  inset: 0;
  background: rgba(255, 0, 85, 0.95);
  color: var(--white);
  font-family: var(--header-font);
  font-size: 1.8em;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: eliminated 1.2s ease-out;
  z-index: 10;
}
@keyframes eliminated {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  60% {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 0.9;
    transform: scale(1) rotate(0deg);
  }
}
.project-item .project-img {
  width: 100%; height: 140px;
  object-fit: cover;
  filter: blur(0.5px);
  transition: filter 0.3s;
}
.project-item .project-number {
  position: absolute; top: 0.6em; left: 0.6em; z-index: 2;
  font-family: var(--header-font);
  font-size: 1.2em;
  background: var(--pink);
  color: var(--white);
  border-radius: 0.5em;
  padding: 0.2em 0.8em;
  box-shadow: 0 0 6px var(--guard-black);
  opacity: 0.9;
}
.project-item .case-study {
  display: none;
  position: absolute; inset: 0;
  background: rgba(0,0,0,0.94);
  color: var(--mint);
  padding: 1.1em;
  font-size: 0.98em;
  z-index: 4;
  animation: glitch 1.2s linear;
}
.project-item.expanded .case-study {
  display: block;
}
.project-item.eliminated:hover::after {
  content: "Eliminated!";
  position: absolute; inset: 0;
  background: rgba(255,0,85,0.9);
  color: var(--white);
  font-family: var(--header-font);
  font-size: 2em;
  display: flex; align-items: center; justify-content: center;
  animation: eliminated 1.2s;
}
@keyframes eliminated {
  0% { opacity: 0; transform: scale(0.5) }
  60% { opacity: 1; transform: scale(1.1) }
  100% { opacity: 0.8; transform: scale(1) }
}

/* Section: Contact Arena */
#contact {
  background: var(--black);
  color: var(--mint);
  padding: 4em 0 2em;
  text-align: center;
}
.contact-icons {
  display: flex; justify-content: center; gap: 2.5em; margin-bottom: 2em;
}
.icon-btn {
  width: 4.2em; height: 4.2em;
  background: var(--mint);
  border-radius: 1em;
  border: none;
  transition: background 0.4s;
  position: relative;
  font-size: 2em;
  cursor: pointer;
  box-shadow: 0 0 20px var(--mint);
}
.icon-btn:active, .icon-btn.clicked {
  animation: guard-morph 1.2s linear;
  background: var(--guard-red);
}
@keyframes guard-morph {
  0% { background: var(--mint); }
  50% { background: var(--guard-black); }
  100% { background: var(--guard-red); }
}

#contact-form {
  background: var(--white);
  color: var(--black);
  border-radius: 1.4em;
  padding: 2em;
  max-width: 380px; margin: 0 auto;
  box-shadow: 0 0 24px var(--mint);
  display: flex; flex-direction: column; gap: 1.2em;
}
#contact-form label {
  display: flex; flex-direction: column; gap: 0.3em;
  font-size: 1em;
  font-weight: 600;
}
#contact-form input, #contact-form textarea {
  border: 2px solid var(--mint);
  border-radius: 0.6em;
  padding: 0.6em;
  font-family: var(--body-font);
  font-size: 1em;
  background: var(--white);
  color: var(--black);
  transition: border 0.3s;
}
#contact-form input:invalid, #contact-form textarea:invalid {
  animation: shake 0.8s cubic-bezier(0.36,0.07,0.19,0.97) both;
  border-color: var(--pink);
}
@keyframes shake {
  10%, 90% { transform: translateX(-1px); }
  20%, 80% { transform: translateX(2px); }
  30%, 50%, 70% { transform: translateX(-4px); }
  40%, 60% { transform: translateX(4px); }
}
.submit-btn {
  background: var(--pink);
  color: var(--white);
  font-family: var(--header-font);
  font-size: 1.3em;
  border: none;
  border-radius: 0.8em;
  padding: 0.5em 1.5em;
  cursor: pointer;
  box-shadow: 0 0 10px var(--pink);
  transition: background 0.3s;
}
.submit-btn:hover, .submit-btn:focus {
  background: var(--mint);
  color: var(--black);
  box-shadow: 0 0 22px var(--mint);
}

/* 404 Game Over - Only shown when created dynamically */
#game-over-modal {
  position: fixed;
  inset: 0;
  z-index: 2000;
  background: rgba(0,0,0,0.98);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5em;
  animation: glitch 1.6s infinite linear;
  color: var(--white);
  font-family: var(--header-font);
}

/* Debug: Ensure main content is visible */
body {
  overflow-x: hidden;
}
main {
  display: block !important;
  visibility: visible !important;
}
.guard-mask-anim {
  width: 120px; height: 120px;
  background: url('assets/guard-mask.svg') center/contain no-repeat;
  animation: guard-mask 2.5s infinite alternate;
}
@keyframes guard-mask {
  0% { filter: grayscale(1) contrast(1.2); }
  100% { filter: grayscale(0) contrast(2); }
}

/* Custom Cursor */
#custom-cursor {
  position: fixed;
  width: 20px;
  height: 20px;
  background: rgba(255, 0, 85, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 9999;
  transform: translate(-50%, -50%);
  transition: all 0.1s ease;
  mix-blend-mode: difference;
}
body {
  cursor: none;
}
a, button, .carousel-card, .player-badge, .project-item {
  cursor: none;
}

/* Scroll Progress Blood Droplet */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: calc(var(--scroll, 0) * 100%);
  height: 4px;
  background: linear-gradient(90deg, var(--pink), var(--mint));
  z-index: 1000;
  transition: width 0.1s ease;
}

/* NFC Activation Animation */
@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  20% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.1);
  }
  80% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
}
#custom-cursor {
  position: fixed; pointer-events: none; z-index: 2000;
  width: 36px; height: 36px;
  background: rgba(255,0,85,0.2);
  border: 2px solid var(--pink);
  border-radius: 50%;
  transform: translate(-50%, -50%) scale(1);
  transition: background 0.2s, border 0.2s, transform 0.15s;
  mix-blend-mode: lighten;
}

/* Scroll Progress (Blood Droplet) */
::-webkit-scrollbar-track { background: var(--black); }
::-webkit-scrollbar-thumb {
  background: var(--pink);
  border-radius: 1.5em;
}
body::after {
  content: '';
  display: block;
  position: fixed;
  top: 0; left: 0; width: 8px;
  height: calc(var(--scroll) * 100vh);
  background: linear-gradient(var(--pink), #a2003d);
  border-radius: 8px;
  z-index: 1800;
  transition: height 0.2s;
}

/* Glitch Keyframes */
@keyframes glitch {
  0% { filter: none; transform: translate(0,0) }
  10% { filter: blur(1.5px) hue-rotate(5deg); }
  20% { transform: translate(-3px,2px) skew(0.5deg) }
  40% { transform: translate(2px,-4px) skew(-1deg) }
  60% { transform: translate(-4px,3px) }
  80% { filter: hue-rotate(-8deg); }
  100% { filter: none; transform: translate(0,0) }
}

/* Responsive & Mobile-first */
@media (max-width: 600px) {
  .carousel-3d { max-width: 97vw; }
  .project-grid { grid-template-columns: 1fr; }
  #contact-form { padding: 1em; }
  .player-badge { font-size: 1.3rem; }
}

/* Dark Mode */
[data-theme="dark"] {
  --black: #232323;
  --white: #0e0e0e;
  --mint: #00ffaa;
  --pink: #ff0055;
  background: var(--black);
  color: var(--white);
}

/* High Contrast */
[data-contrast="high"] {
  --black: #000;
  --white: #fff;
  --mint: #00ffae;
  --pink: #ff0055;
}
/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *, *:before, *:after {
    transition: none !important;
    animation: none !important;
  }
}