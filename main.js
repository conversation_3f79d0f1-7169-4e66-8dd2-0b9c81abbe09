// --- Fonts are loaded via HTML head <link> ---

// Register Service Worker for PWA
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Utility: Animated Player Number
document.addEventListener("DOMContentLoaded", () => {
  console.log('DOM Content Loaded - Starting initialization');

  // 404 modal is now created dynamically, so no need to hide it on load
  console.log('No 404 modal to hide - will be created when needed');

  // Ensure main content is visible
  const main = document.querySelector('main');
  if (main) {
    main.style.display = 'block';
    main.style.visibility = 'visible';
    console.log('Main content made visible');
  }

  // Animated player number
  const pn = document.getElementById('player-number');
  if (pn) {
    pn.textContent = "Player " + (Math.floor(Math.random() * 999) + 1).toString().padStart(3, '0');
    console.log('Player number set');
  }

  // Accessibility toggles
  document.getElementById('contrast-toggle').onclick = () => {
    document.documentElement.toggleAttribute("data-contrast");
    window.localStorage.setItem('contrast', document.documentElement.hasAttribute("data-contrast") ? "high" : "");
  };
  document.getElementById('motion-toggle').onclick = () => {
    document.documentElement.toggleAttribute("data-reduced-motion");
    window.localStorage.setItem('reducedMotion', document.documentElement.hasAttribute("data-reduced-motion") ? "1" : "");
  };
  document.getElementById('dark-toggle').onclick = () => {
    let theme = document.documentElement.getAttribute('data-theme');
    document.documentElement.setAttribute("data-theme", theme === "dark" ? "light" : "dark");
    window.localStorage.setItem('theme', theme === "dark" ? "light" : "dark");
  };
  // Restore saved
  if (localStorage.getItem("contrast") === "high") document.documentElement.setAttribute("data-contrast", "high");
  if (localStorage.getItem("reducedMotion") === "1") document.documentElement.setAttribute("data-reduced-motion", "1");
  if (localStorage.getItem("theme") === "dark") document.documentElement.setAttribute("data-theme", "dark");

  // Custom cursor logic
  const cursor = document.getElementById('custom-cursor');
  document.addEventListener('mousemove', e => {
    cursor.style.left = e.clientX + 'px';
    cursor.style.top = e.clientY + 'px';
  });
  document.body.addEventListener('mouseover', e => {
    if (e.target.closest("a,button,.carousel-card,.player-badge,.project-item")) {
      cursor.style.transform = "translate(-50%, -50%) scale(1.4)";
      cursor.style.background = "rgba(255,0,85,0.5)";
    } else {
      cursor.style.transform = "translate(-50%, -50%) scale(1)";
      cursor.style.background = "rgba(255,0,85,0.2)";
    }
  });

  // Scroll progress (blood droplet)
  document.addEventListener('scroll', () => {
    const scroll = Math.min(1, window.scrollY / (document.body.scrollHeight - window.innerHeight));
    document.body.style.setProperty('--scroll', scroll);
  });

  // Particle.js background (player-number shapes)
  particlesJS("particles-js", {
    particles: {
      number: { value: 56, density: { enable: true, value_area: 800 } },
      color: { value: "#ff0055" },
      shape: {
        type: "polygon",
        polygon: { nb_sides: 4 }
      },
      opacity: { value: 0.2, random: true },
      size: { value: 24, random: true },
      move: { enable: true, speed: 2, direction: "none", out_mode: "out" }
    },
    interactivity: {
      detect_on: "canvas",
      events: { onhover: { enable: true, mode: "repulse" } },
      modes: { repulse: { distance: 80 } }
    },
    retina_detect: true
  });

  // Audio (Howler.js) - Using placeholder sounds for now
  window.sounds = {
    bg: new Howl({
      src: ['data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'],
      loop: true,
      volume: 0.18,
      preload: true
    }),
    nfc: new Howl({
      src: ['data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'],
      volume: 0.8
    }),
    hover: new Howl({
      src: ['data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'],
      volume: 0.5
    }),
    click: new Howl({
      src: ['data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT'],
      volume: 0.5
    })
  };

  // Start background music after user interaction
  let musicStarted = false;
  const startMusic = () => {
    if (!musicStarted) {
      window.sounds.bg.play();
      musicStarted = true;
    }
  };
  document.addEventListener('click', startMusic, { once: true });
  document.addEventListener('touchstart', startMusic, { once: true });

  // NFC detection
  if (window.NDEFReader) {
    navigator.permissions.query({ name: "nfc" })
      .then(perm => {
        if (perm.state === "granted") {
          const nfc = new NDEFReader();
          nfc.scan().then(() => {
            window.sounds.nfc.play();
            startParticleActivation();
            showNFCActivation();
          }).catch(err => {
            console.log('NFC scan error:', err);
          });
        }
      });
  }

  // Microinteractions
  document.querySelectorAll('.social-icon').forEach(icon => {
    icon.addEventListener('mouseenter', () => window.sounds.hover.play());
    icon.addEventListener('click', () => window.sounds.click.play());
  });
  document.querySelectorAll('.carousel-card').forEach(card => {
    card.addEventListener('mouseenter', () => window.sounds.hover.play());
    card.addEventListener('click', () => window.sounds.click.play());
  });

  // Player badge hover = ID effect
  document.querySelector('.player-badge').addEventListener('mouseenter', () => {
    window.sounds.hover.play();
  });

  // Link Hub: 3D Carousel (CSS-only for flipping, simple JS for rotation)
  const carousel = document.querySelector('.carousel-3d');
  if (carousel) {
    // Simulate a simple 3-card carousel (replace with dynamic if desired)
    let cur = 0;
    const cards = Array.from(carousel.children);
    function updateCarousel() {
      cards.forEach((card, i) => {
        const theta = ((i - cur) * 40);
        card.style.setProperty('--rotation', `${theta}deg`);
        card.style.setProperty('--z', i === cur ? 5 : 1);
      });
    }
    carousel.addEventListener('click', () => {
      cur = (cur + 1) % cards.length;
      updateCarousel();
      window.sounds.click.play();
    });
    updateCarousel();
  }

  // Project Showcase: expand/collapse on click
  document.querySelectorAll('.project-item').forEach(item => {
    item.addEventListener('click', () => {
      item.classList.toggle('expanded');
      window.sounds.click.play();
    });
  });

  // Contact icons morph to guards
  document.querySelectorAll('.icon-btn').forEach(btn => {
    btn.addEventListener('click', () => {
      btn.classList.add('clicked');
      setTimeout(() => btn.classList.remove('clicked'), 1200);
      window.sounds.click.play();
    });
  });

  // Contact form validation
  const form = document.getElementById('contact-form');
  form.addEventListener('submit', e => {
    let valid = true;
    [...form.elements].forEach(el => {
      if ((el.required && !el.value) || (el.type === "email" && !el.value.match(/^[^@]+@[^@]+\.[^@]+$/))) {
        el.classList.add('invalid');
        valid = false;
      } else {
        el.classList.remove('invalid');
      }
    });
    if (!valid) {
      e.preventDefault();
      window.sounds.nfc.play();
      // Animate shake
      form.classList.remove('shake');
      void form.offsetWidth; // reflow
      form.classList.add('shake');
    }
  });

  // 404 Game Over modal - Create dynamically when needed
  window.showGameOver = () => {
    let modal = document.getElementById('game-over-modal');
    if (!modal) {
      modal = document.createElement('div');
      modal.id = 'game-over-modal';
      modal.setAttribute('aria-modal', 'true');
      modal.setAttribute('role', 'dialog');
      modal.innerHTML = `
        <div class="guard-mask-anim"></div>
        <h1>Game Over</h1>
        <p>404: The page you are looking for has been eliminated.</p>
        <button id="close-404">Retry</button>
      `;
      document.body.appendChild(modal);

      document.getElementById('close-404').onclick = () => {
        modal.remove();
      };
    }
    modal.hidden = false;
    if (window.sounds && window.sounds.nfc) {
      window.sounds.nfc.play();
    }
  };
});

// NFC activation: Particle explosion
function startParticleActivation() {
  const canvas = document.getElementById('particle-canvas');
  if (!canvas) return;

  // Create particle explosion effect
  const ctx = canvas.getContext('2d');
  canvas.width = canvas.offsetWidth;
  canvas.height = canvas.offsetHeight;

  const particles = [];
  for (let i = 0; i < 50; i++) {
    particles.push({
      x: canvas.width / 2,
      y: canvas.height / 2,
      vx: (Math.random() - 0.5) * 10,
      vy: (Math.random() - 0.5) * 10,
      life: 1,
      decay: Math.random() * 0.02 + 0.01,
      color: Math.random() > 0.5 ? '#ff0055' : '#00ffaa'
    });
  }

  function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    particles.forEach((p, index) => {
      p.x += p.vx;
      p.y += p.vy;
      p.life -= p.decay;

      if (p.life <= 0) {
        particles.splice(index, 1);
        return;
      }

      ctx.globalAlpha = p.life;
      ctx.fillStyle = p.color;
      ctx.fillRect(p.x, p.y, 4, 4);
    });

    if (particles.length > 0) {
      requestAnimationFrame(animate);
    }
  }

  animate();
}

// Show NFC activation message
function showNFCActivation() {
  const message = document.createElement('div');
  message.style.cssText = `
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 85, 0.95);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    font-family: 'Bebas Neue', sans-serif;
    font-size: 2rem;
    z-index: 10000;
    animation: fadeInOut 3s ease-in-out forwards;
  `;
  message.textContent = 'NFC DETECTED!';
  document.body.appendChild(message);

  setTimeout(() => {
    document.body.removeChild(message);
  }, 3000);
}

// GDPR-compliant analytics (minimal, opt-in)
(function(){
  if (!window.localStorage.getItem("analytics-consent")) {
    // Show banner, etc. (omitted for brevity)
  }
  // Only load analytics if consented
  if (window.localStorage.getItem("analytics-consent") === "yes") {
    // Minimal script, e.g. Plausible/SimpleAnalytics
    var s = document.createElement("script");
    s.src = "https://plausible.io/js/plausible.js";
    s.defer = true; s.setAttribute("data-domain", "yourdomain.com");
    document.head.appendChild(s);
  }
})();

// Image Upload Functionality for Player Card
function initializeImageUpload() {
  const imageUpload = document.getElementById('imageUpload');
  const playerImage = document.getElementById('playerImage');
  const uploadPlaceholder = document.getElementById('uploadPlaceholder');
  const imageUploadArea = document.querySelector('.image-upload-area');

  if (!imageUpload || !playerImage || !uploadPlaceholder || !imageUploadArea) {
    console.log('Image upload elements not found');
    return;
  }

  imageUpload.addEventListener('change', function(event) {
    const file = event.target.files[0];

    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader();

      reader.onload = function(e) {
        // Hide placeholder and show image
        uploadPlaceholder.style.display = 'none';
        playerImage.src = e.target.result;
        playerImage.style.display = 'block';
        imageUploadArea.classList.add('has-image');

        // Play sound effect
        if (window.sounds && window.sounds.click) {
          window.sounds.click.play();
        }

        // Add a subtle animation
        playerImage.style.opacity = '0';
        setTimeout(() => {
          playerImage.style.transition = 'opacity 0.5s ease-in-out';
          playerImage.style.opacity = '1';
        }, 100);

        console.log('Player image uploaded successfully');
      };

      reader.readAsDataURL(file);
    } else {
      alert('Please select a valid image file.');
    }
  });

  // Allow clicking on the image to change it
  playerImage.addEventListener('click', function() {
    imageUpload.click();
  });
}

// Initialize image upload when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  initializeImageUpload();
});
