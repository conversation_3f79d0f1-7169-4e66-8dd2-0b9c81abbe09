const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 8080;

app.use(express.static(path.join(__dirname)));

app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'Index.html'));
});

// Handle case-insensitive routing for index
app.get('/index.html', (req, res) => {
  res.sendFile(path.join(__dirname, 'Index.html'));
});

// 404 handler with Squid Game theme
app.use((req, res) => {
  res.status(404).sendFile(path.join(__dirname, 'Index.html'));
});

app.listen(PORT, () => {
  console.log(`Server running at http://localhost:${PORT}`);
});
